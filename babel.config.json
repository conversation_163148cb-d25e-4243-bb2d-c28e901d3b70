{
  "presets": [
    [
      "@babel/preset-env",
      {}
    ],
    [
      "@babel/preset-react",
      {
        "runtime": "automatic",
        "importSource": "@emotion/react"
      }
    ],
    "@babel/preset-typescript"
  ],
  "plugins": [
    [
      "@babel/plugin-proposal-decorators",
      {
        "legacy": true
      }
    ],
    "@babel/plugin-proposal-class-properties",
    /*[
      "babel-plugin-styled-components",
      {
        "ssr": false,
        "namespace": "kylin-bi",
        "displayName": true,
        "fileName": false
      }
    ],*/
    ["@emotion/babel-plugin"]
  ],
  "assumptions": {
    "setPublicClassFields": false
  }
}
