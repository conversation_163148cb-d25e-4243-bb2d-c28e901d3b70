/* eslint-disable max-lines */
import { action, computed, makeObservable, observable, reaction, toJS } from 'mobx'
import { StyleProperties } from '@component-arranger/widget/lib/core/types'
import { isEqual, omit } from 'lodash-es'
import { FilterSetDisplayMode, WidgetType } from '@/dashboards/widgets/type'
import { getDefaultStyle } from '../initial-data'
import { mergeObject } from '@/dashboards/src/utils'
import { TimeGranularityModel } from '@/dashboards/widgets/time-granualarity/model'

export interface WidgetDTO<D extends Record<string, any> = {}> {
  args: { data: D; style?: StyleProperties; type: WidgetType }
  /**
   * title 是否被用户手动修改过
   */
  isTitleModified?: boolean
  id: string
  region?: Region
}

export interface FormField {
  name: string[]
  value: any
  touched?: boolean
  validating?: boolean
  errors?: string[]
}

export type FormFiledStatus = Omit<FormField, 'value'>

export type Region = Partial<{
  static?: boolean
  x: number
  y: number
  w: number
  h: number
  minW?: number
  minH?: number
  maxW?: number
  maxH?: number
}>

export const DefaultRegion = { w: 2, h: 2 }

const annotations = {
  data: observable,
  style: observable,
  region: observable,
  formFieldStatus: observable,
  isTitleModified: observable,
  toggleStatic: action,
  updateData: action,
  updateStyle: action,
  updateRegion: action,
  updateDataAndStyle: action,
  updateFormFieldStatus: action,
  dto: computed,
  formFields: computed,
  update: action,
  field: observable,
  updateField: action,
  fromFormFields: action,
  smartUpdateTitle: action,
  markTitleAsModified: action,
  unlinkWidgets: action,
  linkFilter: action,
  linkFilters: action,
  unlinkFilter: action,
  unlinkFilters: action,
  linkChart: action,
  linkCharts: action,
  unlinkChart: action,
  unlinkCharts: action,
  linkKpi: action,
  linkKpis: action,
  unlinkKpi: action,
  unlinkKpis: action,
  linkParameter: action,
  unlinkParameter: action,
}

export abstract class WidgetModelBase<D extends Record<string, any> = {}, F extends Record<string, any> = {}> {
  data: D = {} as D

  style: StyleProperties

  region: Region

  field: F = {} as F

  formFieldStatus: FormFiledStatus[] = []

  disposers: Array<() => void> = []

  /**
   * title 是否被用户手动修改过
   */
  isTitleModified = false

  /**
   * 标记 title 被用户手动修改过
   */
  markTitleAsModified() {
    this.isTitleModified = true
  }

  /**
   * 智能更新 title
   * @param newTitle
   */
  smartUpdateTitle(newTitle: string) {
    if (!this.isTitleModified) {
      this.style.title.text = newTitle
    }
  }

  get formFields() {
    // 暂时不保存校验信息
    // const fields = _fields.map((field) => {
    //   const status = this.formFieldStatus.find((s) => s.name.join('.') === field.name.join('.'))
    //
    //   return {
    //     ...field,
    //     ...status,
    //   }
    // })

    return flatten({
      data: this.data,
      style: this.style,
    })
  }

  /**
   * @deprecated
   *
   * 考虑到 BI 仪表盘的组件设置需要实时保存，并且还需要实时校验，
   * 所以暂时放弃使用 Form 的 onFieldsChange 来更新数据，
   * 而是使用 Form 的 onValuesChange 来更新数据
   *
   * @param fields
   */
  fromFormFields(fields: FormField[]) {
    const dataAndStyle = unFlatten(fields) as any

    this.updateDataAndStyle(dataAndStyle)

    this.updateFormFieldStatus(fields.map((field) => omit(field, 'value')))
  }

  protected constructor(public id: string, public type: WidgetType, dto?: WidgetDTO<D>) {
    makeObservable(this, annotations)

    // filterSetDisplayMode 是 筛选器组合 的特有属性
    const filterSetStyle = type === WidgetType.FilterSet ? { filterSetDisplayMode: FilterSetDisplayMode.Collapse } : {}

    this.style = { ...getDefaultStyle(), ...filterSetStyle }
    this.region = { ...DefaultRegion }
    if (dto) {
      this.data = mergeObject(this.data, dto.args?.data || {})
      this.style = mergeObject(this.style, { ...filterSetStyle, ...dto.args?.style })
      this.region = dto.region || this.region
      this.isTitleModified = dto.isTitleModified || false
    }
  }

  afterCopy(_isCurrentPage: boolean) {}

  unlinkWidgets = (widgetIds: string[]) => {
    const { type, data } = this as any
    const widgetTypes = [
      WidgetType.Filter,
      WidgetType.Chart,
      WidgetType.KPI,
      WidgetType.Parameter,
      WidgetType.FilterSet,
    ]
    const fields = ['filters', 'charts', 'kpis', 'parameters', 'filterSets']

    if (widgetTypes.includes(type)) {
      for (const field of fields) {
        if (data[field]) {
          data[field] = data[field].filter((id: string) => !widgetIds.includes(id))
        }
      }

      if (data.granularity && widgetIds.includes(data.granularity)) {
        data.granularity = ''
      }
    }
  }

  /**
   * 与 参数 建立关联
   * @param parameterId parameter id
   */
  linkParameter = (parameterId: string) => {
    const _this = this as any

    if ([WidgetType.Chart, WidgetType.KPI, WidgetType.Filter, WidgetType.FilterSet].includes(this.type)) {
      const prev = this.data.parameters || []
      _this.data.parameters = Array.from(new Set([...prev, parameterId]))
    } else {
      throw new Error('only Chart, KPI, Filter, FilterSet can link parameter')
    }
  }

  /**
   * 与 parameter 解除关联
   * @param parameterId parameter id
   */
  unlinkParameter = (parameterId: string) => {
    const _this = this as any

    if ([WidgetType.Chart, WidgetType.KPI, WidgetType.Filter, WidgetType.FilterSet].includes(this.type)) {
      const prev = this.data.parameters || []
      _this.data.parameters = prev.filter((id: any) => id !== parameterId)
    }
  }

  /**
   * 与 filter 建立关联
   * @param filterId filter id
   */
  linkFilter = (filterId: string) => {
    const _this = this as any

    if ([WidgetType.Filter, WidgetType.Chart, WidgetType.KPI, WidgetType.Parameter].includes(this.type)) {
      if (this.type === WidgetType.Filter && _this.isDateDimension) {
        return
      }
      const prev = this.data.filters || []
      _this.data.filters = Array.from(new Set([...prev, filterId]))
    } else {
      throw new Error('only Chart, KPI, Filter, Parameter can link filter')
    }
  }

  /**
   * 批量与 filter 建立关联
   */
  linkFilters = (filterIds: string[]) => {
    const _this = this as any

    if ([WidgetType.Filter, WidgetType.Chart, WidgetType.KPI].includes(this.type)) {
      if (this.type === WidgetType.Filter && _this.isDateDimension) {
        return
      }
      const prev = this.data.filters || []
      _this.data.filters = Array.from(new Set([...prev, ...filterIds]))
    } else {
      throw new Error('only Chart, KPI, Filter can link filter')
    }
  }

  /**
   * 与 filter 解除关联
   * @param filterId filter id
   */
  unlinkFilter = (filterId: string) => {
    const _this = this as any

    if ([WidgetType.Filter, WidgetType.Chart, WidgetType.KPI, WidgetType.Parameter].includes(this.type)) {
      const prev = this.data.filters || []
      _this.data.filters = prev.filter((id: any) => id !== filterId)
    }
  }

  /**
   * 与 filterSet 建立关联
   */
  linkFilterSet = (filterId: string) => {
    const _this = this as any

    if ([WidgetType.Parameter].includes(this.type)) {
      const prev = this.data.filterSets || []
      _this.data.filterSets = Array.from(new Set([...prev, filterId]))
    } else {
      throw new Error('only Parameter can link filterSet')
    }
  }

  /**
   * 与 filterSet 解除关联
   */
  unlinkFilterSet = (filterId: string) => {
    const _this = this as any

    if ([WidgetType.Parameter].includes(this.type)) {
      const prev = this.data.filterSets || []
      _this.data.filterSets = prev.filter((id: any) => id !== filterId)
    }
  }

  /**
   * 批量与 filter 解除关联
   * @param filterIds
   */
  unlinkFilters = (filterIds: string[]) => {
    const _this = this as any

    if ([WidgetType.Filter, WidgetType.Chart, WidgetType.KPI].includes(this.type)) {
      const prev = this.data.filters || []
      _this.data.filters = prev.filter((id: any) => !filterIds.includes(id))
    } else {
      throw new Error('only Chart, KPI, Filter can unlink filter')
    }
  }

  /**
   * 与 chart 建立关联
   * @param chartId chart id
   */
  linkChart = (chartId: string) => {
    const _this = this as any

    if ([WidgetType.Filter, WidgetType.TimeGranularity, WidgetType.Parameter].includes(this.type)) {
      const prev = this.data.charts || []
      _this.data.charts = Array.from(new Set([...prev, chartId]))
    } else {
      throw new Error('only Filter and TimeGranularity and Parameter can link chart')
    }
  }

  /**
   * 批量与 chart 建立关联
   */
  linkCharts = (chartIds: string[]) => {
    const _this = this as any

    if ([WidgetType.Filter, WidgetType.TimeGranularity].includes(this.type)) {
      const prev = this.data.charts || []
      _this.data.charts = Array.from(new Set([...prev, ...chartIds]))
    } else {
      throw new Error('only Filter and TimeGranularity can link chart')
    }
  }

  /**
   * 与 chart 解除关联
   * @param chartId chart id
   */
  unlinkChart = (chartId: string) => {
    const _this = this as any

    if ([WidgetType.Filter, WidgetType.TimeGranularity, WidgetType.Parameter].includes(this.type)) {
      const prev = this.data.charts || []
      _this.data.charts = prev.filter((id: any) => id !== chartId)
    } else {
      throw new Error('only Filter and TimeGranularity and Parameter can unlink chart')
    }
  }

  /**
   * 批量与 chart 解除关联
   * @param chartIds
   */
  unlinkCharts = (chartIds: string[]) => {
    const _this = this as any

    if ([WidgetType.Filter, WidgetType.TimeGranularity].includes(this.type)) {
      const prev = this.data.charts || []
      _this.data.charts = prev.filter((id: any) => !chartIds.includes(id))
    } else {
      throw new Error('only Filter and TimeGranularity can unlink charts')
    }
  }

  /**
   * 与 kpi 建立关联
   * @param kpiId kpi id
   */
  linkKpi = (kpiId: string) => {
    const _this = this as any

    if ([WidgetType.Filter, WidgetType.Parameter].includes(this.type)) {
      const prev = this.data.kpis || []
      _this.data.kpis = Array.from(new Set([...prev, kpiId]))
    } else {
      throw new Error('only Filter and Parameter can link kpi')
    }
  }

  /**
   * 批量与 kpi 建立关联
   */
  linkKpis = (kpiIds: string[]) => {
    const _this = this as any

    if ([WidgetType.Filter].includes(this.type)) {
      const prev = this.data.kpis || []
      _this.data.kpis = Array.from(new Set([...prev, ...kpiIds]))
    } else {
      throw new Error('only Filter can link kpi')
    }
  }

  /**
   * 与 kpi 解除关联
   * @param kpiId kpi id
   */
  unlinkKpi = (kpiId: string) => {
    const _this = this as any

    if ([WidgetType.Filter, WidgetType.Parameter].includes(this.type)) {
      const prev = this.data.kpis || []
      _this.data.kpis = prev.filter((id: any) => id !== kpiId)
    } else {
      throw new Error('only Filter and Parameter can unlink kpi')
    }
  }

  /**
   * 批量与 kpi 解除关联
   * @param kpiIds
   */
  unlinkKpis = (kpiIds: string[]) => {
    const _this = this as any

    if ([WidgetType.Filter].includes(this.type)) {
      const prev = this.data.kpis || []
      _this.data.kpis = prev.filter((id: any) => !kpiIds.includes(id))
    } else {
      throw new Error('only Filter can unlink kpi')
    }
  }

  /**
   * 与 granularity 建立关联
   * @param granularityId
   */
  linkGranularity = (granularityId: string) => {
    const _this = this as any

    if ([WidgetType.Chart].includes(this.type)) {
      const data = _this.data

      if (data.granularity && data.granularity !== granularityId) {
        // 如果报表关联着其他的时间粒度，需要解除关联
        const granularity = _this.parent.findWidgetGlobally(data.granularity) as TimeGranularityModel
        granularity?.unlinkChart(_this.id)
      }

      _this.data.granularity = granularityId
    } else {
      throw new Error('only Chart can link granularity')
    }
  }

  /**
   * 与 granularity 解除关联
   */
  unlinkGranularity = (granularityId: string) => {
    const _this = this as any

    if ([WidgetType.Chart].includes(this.type)) {
      if (_this.data.granularity === granularityId) {
        _this.data.granularity = ''
      }
    } else {
      throw new Error('only Chart can unlink granularity')
    }
  }

  watchWithLog = (label: string, x: any) => {
    const disposer = reaction(
      () => [x],
      ([x]) => {
        // eslint-disable-next-line no-console
        console.log(`${label} changed`, toJS(x))
      },
      { equals: isEqual },
    )

    this.collectDisposer(disposer)
  }

  collectDisposer(disposer: () => void) {
    this.disposers.push(disposer)
  }

  dispose() {
    this.disposers.forEach((disposer) => disposer())
    this.disposers = []
  }

  toggleStatic() {
    this.region = {
      ...this.region,
      static: !this.region?.static,
    }
  }

  updateData(value: Partial<D>) {
    this.data = mergeObject(this.data, value)
  }

  /**
   * 根据路径更新数据
   *
   * @param value 任意值
   *
   */
  // updateDataByPath(path: string[], value: any) {}

  updateFormFieldStatus(value: FormFiledStatus[]) {
    this.formFieldStatus = value
  }

  updateField(value: Record<string, any>) {
    this.field = mergeObject(this.field, value)
  }

  updateStyle(value: StyleProperties) {
    this.style = mergeObject(this.style, value)
  }

  updateRegion(value: Region) {
    this.region = value
  }

  update(data: Partial<WidgetDTO<D>>) {
    this.updateData(data.args?.data || {})
    this.updateStyle(data.args?.style || {})
    this.updateRegion(data.region || { ...DefaultRegion })
  }

  updateDataAndStyle(data: { data: D; style: StyleProperties }) {
    this.updateData(data.data)
    this.updateStyle(data.style)
  }

  getDTO(): WidgetDTO<D> {
    return {
      args: {
        type: this.type,
        data: this.data,
        style: this.style,
      },
      isTitleModified: this.isTitleModified,
      id: this.id,
      region: this.region,
    }
  }

  get dto(): WidgetDTO<D> {
    return this.getDTO()
  }
}

interface PartialField {
  name: string[]
  value: any
}

export function flatten<T extends Record<string, any>>(obj: T): PartialField[] {
  const fields: PartialField[] = []

  for (const [key, value] of Object.entries(obj)) {
    if (value !== null && Object.prototype.toString.call(value) === '[object Object]') {
      fields.push(
        ...flatten(value).map((field) => ({
          ...field,
          name: [key, ...field.name],
        })),
      )
    } else {
      fields.push({
        name: [key],
        value,
      })
    }
  }

  return fields
}

export function unFlatten<T extends Record<string, any>>(fields: PartialField[]): T {
  const obj: Record<string, any> = {}

  for (const field of fields) {
    const [key, ...rest] = field.name

    if (rest.length === 0) {
      obj[key] = field.value
    } else {
      obj[key] = unFlatten([{ ...field, name: rest }])
    }
  }

  return obj as T
}
