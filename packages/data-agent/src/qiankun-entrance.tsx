import { IPortalConfig } from '@shuyun-ep-team/portal/portal-service'

let G!: {
  mount: (boxDom: HTMLDivElement, conf: IPortalConfig) => Promise<unknown>
  unmount: (boxDom: HTMLDivElement) => Promise<unknown>
  container: HTMLDivElement
}

// 只会执行一次
export function bootstrap() {
  return Promise.resolve()
}

// 每次装载都会执行
export function mount(config: IPortalConfig) {
  return import('./app').then(({ mount: appMount, unmount: appUnmount }) => {
    const container = document.createElement('div')
    container.setAttribute('style', 'width: 100%; height: 100%; position: relative; overflow: auto;')

    G = { mount: appMount, unmount: appUnmount, container }

    config.SpaService.getRoot().then((root: HTMLDivElement) => {
      root.appendChild(G.container)

      appMount(G.container, config)
    })
  })
}

// 卸载应用
export function unmount() {
  return G.unmount(G.container).then(() => {
    removeDOM(G.container)

    // 移除全局属性
    Reflect.deleteProperty(G, 'mount')
    Reflect.deleteProperty(G, 'unmount')
    Reflect.deleteProperty(G, 'container')
  })
}

function removeDOM(dom: HTMLDivElement) {
  if (dom.remove) {
    return dom.remove()
  }

  if (dom.parentNode) {
    dom.parentNode.removeChild(dom)
  }

  return undefined
}

export default {}
