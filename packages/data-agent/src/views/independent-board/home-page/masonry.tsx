import React, { useMemo } from 'react'

export interface MasonryProps<T> {
  items: T[]
  columnCount?: number
  gap?: number
  renderItem: (item: T, index: number) => React.ReactNode
}

export function Masonry<T>({ items, columnCount = 3, gap = 16, renderItem }: MasonryProps<T>) {
  const columns = useMemo(() => {
    const rowCount = Math.ceil(items.length / columnCount)
    const cols: T[][] = Array.from({ length: columnCount }, () => [])

    for (let row = 0; row < rowCount; row++) {
      for (let col = 0; col < columnCount; col++) {
        const index = row * columnCount + col
        if (index < items.length) {
          cols[col].push(items[index])
        }
      }
    }

    return cols
  }, [items, columnCount])

  return (
    <div style={{ display: 'flex', gap }}>
      {columns.map((col, colIndex) => (
        <div key={colIndex} style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          {col.map((item, i) => (
            <div key={i}>{renderItem(item, i)}</div>
          ))}
        </div>
      ))}
    </div>
  )
}
