import { Bubble, GetP<PERSON> } from '@shuyun/ai-ui'
import { observer } from 'mobx-react-lite'
import React, { useEffect, useRef, useState } from 'react'
import { css } from '@emotion/react'
import InfiniteScroll from 'react-infinite-scroll-component'
import { Meta, useAIBoardContext } from '../../independent-board-provider'
import { EmptyConversationPlaceHolder, LoadingConversationPlaceHolder } from './empty-conversation'
import { BubbleItem } from './bubble-unit/bubble-item'
import { useSessionDetailsInfinite } from '../../hooks/use-session-details'
import { translate } from '@/business/src/components/translate'
import { CREATEKEY } from '../../../helper/unit'
import { useDynamicConversation } from './hooks/use-dynamic-conversation'

export enum RoleEnum {
  AI = 'ai',
  LOCAL = 'local',
}

export const roles: GetProp<typeof Bubble.List, 'roles'> = {
  [RoleEnum.AI]: {
    placement: 'start',
    typing: { step: 3, interval: 20 },
    styles: {
      content: {
        padding: '0px',
        background: 'transparent',
        width: '100%',
      },
      footer: {
        width: '100%',
      },
    },
  },
  [RoleEnum.LOCAL]: {
    placement: 'end',
    typing: { step: 3, interval: 2000 },
    variant: 'shadow',
    styles: {
      content: {
        padding: '0px',
        background: '#F0F4FD',
      },
    },
  },
}

export const ConversationBubbleList = observer(({ onConfirm }: { onConfirm: (content: string) => void }) => {
  const aiBoardCtx = useAIBoardContext()

  const { currentProjectId, activeConversionId, updateConversionPage } = aiBoardCtx

  // 使用动态会话管理 hook
  const { isCreatingPage, dynamicPages, getCurrentActivePage, updateDynamicPageMeta, updateDynamicPageHistoryMsgs } =
    useDynamicConversation()

  const currentActivePage = getCurrentActivePage()

  // 同步动态实例的数据到原有实例
  useEffect(() => {
    const dynamicPage = dynamicPages.get(activeConversionId)
    if (dynamicPage && currentActivePage && currentActivePage !== dynamicPage) {
      // 如果动态实例有数据，但当前使用的是原有实例，则同步数据
      if (dynamicPage.historyMsgs.length > 0 || dynamicPage.meta.cached) {
        updateConversionPage(activeConversionId, {
          historyMsgs: dynamicPage.historyMsgs,
          meta: dynamicPage.meta,
        })
      }
    }
  }, [activeConversionId, currentActivePage, dynamicPages, updateConversionPage])

  const { historyMsgs, isFetching, fetchNextPage, hasNextPage, isFetchingNextPage, total } = useSessionDetailsInfinite(
    currentProjectId,
    activeConversionId,
    currentActivePage ? currentActivePage?.meta?.isFirst : true,
  )

  const [items, setItem] = useState<GetProp<typeof Bubble.List, 'items'>>([])

  const bottomRef = useRef<HTMLDivElement>(null)
  const hasMountedRef = useRef(false)
  const containerRef = useRef<HTMLDivElement>(null)
  const [shouldAutoScroll, setShouldAutoScroll] = useState(true)
  const prevConversionId = useRef<string | undefined>(activeConversionId)

  useEffect(() => {
    setItem(
      [...(currentActivePage?.historyMsgs || []), ...(currentActivePage?.messages || [])].map((msg) => {
        return {
          key: msg.id,
          typing: msg.typing,
          role: msg.role,
          status: msg.status,
          turnId: msg.turnId,
          input: msg.input,
          content: <BubbleItem msg={msg} onConfirm={onConfirm} />,
        }
      }),
    )
  }, [currentActivePage?.historyMsgs, currentActivePage?.messages, onConfirm])

  useEffect(() => {
    if (bottomRef.current && shouldAutoScroll) {
      requestAnimationFrame(() => {
        bottomRef.current?.scrollIntoView({
          behavior: hasMountedRef.current ? 'smooth' : 'auto',
        })
        hasMountedRef.current = true
      })
    }
  }, [items, shouldAutoScroll])

  // ✅ 滚动容器滚动时，检测是否接近底部
  const handleScroll = () => {
    const el = containerRef.current
    if (!el) return

    const { scrollTop, scrollHeight, clientHeight } = el

    // 离底部 50px 内算作接近底部
    const isAtBottom = scrollHeight - scrollTop - clientHeight < 50

    setShouldAutoScroll(isAtBottom)
  }

  //  一旦切换会话ID，则重置 shouldAutoScroll
  useEffect(() => {
    if (prevConversionId.current !== activeConversionId) {
      setShouldAutoScroll(true)
      prevConversionId.current = activeConversionId
      hasMountedRef.current = false
    }
  }, [activeConversionId])

  useEffect(() => {
    if (!isFetching && currentActivePage && !currentActivePage?.meta?.cached) {
      const meta = {
        ...currentActivePage?.meta,
        cached: true,
      } as Meta

      // 如果是动态创建的实例，手动更新
      if (dynamicPages.has(activeConversionId)) {
        updateDynamicPageMeta(meta)
        updateDynamicPageHistoryMsgs(historyMsgs)
      } else {
        // 如果是原有实例，使用原有方法
        updateConversionPage(activeConversionId, {
          meta,
          historyMsgs,
        })
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeConversionId, isFetching])

  // 如果正在创建页面或没有currentActivePage，显示加载状态
  if (isCreatingPage || (!currentActivePage && activeConversionId !== CREATEKEY) || (isFetching && !items.length)) {
    return <LoadingConversationPlaceHolder />
  }

  return (
    <>
      {items.length || currentActivePage?.meta.fetching ? (
        <div css={messages} ref={containerRef} onScroll={handleScroll}>
          <div
            id='scrollableDiv'
            style={{
              overflow: 'auto',
              display: 'flex',
              flexDirection: 'column-reverse',
              padding: '0 20%',
            }}
          >
            <InfiniteScroll
              dataLength={items.length}
              next={() => fetchNextPage()}
              style={{ display: 'flex', flexDirection: 'column-reverse' }}
              inverse={true}
              hasMore={hasNextPage || false}
              loader={isFetchingNextPage ? <LoadingConversationPlaceHolder /> : null}
              scrollableTarget='scrollableDiv'
              endMessage={
                <div css={endMessage}>{total >= 10 ? translate('enterprise-kylin-bi-1751125603512-7053') : ''}</div>
              }
            >
              <Bubble.List items={items} roles={roles} />
            </InfiniteScroll>
          </div>
          <div ref={bottomRef} />
        </div>
      ) : (
        <EmptyConversationPlaceHolder />
      )}
    </>
  )
})

const messages = css`
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  height: calc(100% - 180px);
  width: 100%;
  .kui-bubble-list {
    overflow-y: unset;
    min-height: calc(100vh - 334px);
  }
`

const endMessage = css`
  text-align: center;
  padding: 16px;
  color: #999;
  font-size: 14px;
`
