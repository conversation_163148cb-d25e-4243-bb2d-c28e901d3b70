import { action, makeObservable, observable, computed } from 'mobx'
import { BaseFieldModel } from '@/business/src/plugins/base/field-model'
import type { IField } from '@/business/src/plugins/type'
import type { DiagramStore } from '@/business/src/plugins/base/diagram'
import { PluginEnum } from '@/business/plugins/type'
import { measureValueField } from '@/business/constant'
import { isMeasureValueField, isNumericalMeasure } from '@/business/src/plugins/utils'
import { FieldModel as MatrixFieldModel } from '../../tables/matrix/field-model'
import { IFunnelMode } from './type'

export class FieldModel extends BaseFieldModel {
  constructor(instance: DiagramStore, chartType: PluginEnum) {
    super(instance, chartType)
    makeObservable(this, {
      dimension: observable,
      enableDimension: computed,
      value: observable,
      funnelMode: observable,
      updateDimension: action,
      updateValue: action,
      updateFunnelMode: action,
    })
  }

  dimension: IField[] = []

  // 根据漏斗模式计算出的可用的维度(原需求：切换值漏斗模式时维度保留1个，但切换回维度漏斗时源数据要留存)
  get enableDimension() {
    switch (this.funnelMode) {
      // 维度漏斗 最多只能有两个维度
      case 'dimension':
        return this.dimension.slice(0, 2)
      // 值漏斗 只能有1个维度
      case 'value':
        return this.dimension.slice(0, 1)
      default:
        return this.dimension
    }
  }

  updateDimension = (dimension: IField[]) => {
    this.dimension = dimension
  }

  value: IField[] = []

  updateValue = (value: IField[]) => {
    this.value = value
  }

  funnelMode: IFunnelMode = 'dimension'

  updateFunnelMode = (mode: string) => {
    this.funnelMode = mode as IFunnelMode
  }

  // 以漏斗图为基准图(载入的第一张图)时, 其它图形重新切换回来触发
  cloneFieldModel(): FieldModel {
    const fieldModel = new FieldModel(this.getInstance(), this.chartType)
    fieldModel.updateDimension([...this.dimension])
    fieldModel.updateValue([...this.value])
    fieldModel.updateFunnelMode(this.funnelMode)
    return fieldModel
  }

  /* 切换到矩阵表格数据转换 */
  toMatrixFieldModel(): MatrixFieldModel {
    const fieldModel = new MatrixFieldModel(this.getInstance(), PluginEnum.Matrix)
    fieldModel.updateRow([...this.dimension])
    fieldModel.updateValue([...this.getMeasure()])
    return fieldModel
  }

  /* 矩阵表格切换到漏斗图 */
  fromMatrixFieldModel(matrixFieldModel: MatrixFieldModel) {
    const matrixDimensionRow = matrixFieldModel.row.filter((r) => !isMeasureValueField(r))
    // 1. 维度漏斗 支持拖入1-2 维度
    // 2. 值漏斗 支持拖入0-1 维度
    // 优先转化成维度漏斗
    this.updateFunnelMode('dimension')
    // 更新维度 取前两个维度
    this.updateDimension(matrixDimensionRow.slice(0, 2))
    // 更新值
    this.updateValue(matrixFieldModel.value.filter((item) => isNumericalMeasure(item)))
  }

  getColumn(): IField[] {
    return []
  }

  getMeasure(): IField[] {
    return [...this.value]
  }

  // 调接口用的row
  getRow(): IField[] {
    return [...this.enableDimension, measureValueField]
  }

  getWatchPropertyList(): string[] {
    return ['dimension', 'value']
  }

  get usedFieldList() {
    return [...this.enableDimension, ...this.value]
  }

  // 抽象类类面有这个方法，不写ts会报错。
  addFieldByClickCube(_field: IField) {}

  // 是否满足作图条件
  canDrawPlot() {
    // 值的数量至少存在1个
    // 值漏斗模式下 只需要1个值就能做图
    // 维度模式下 至少需要1个维度 + 1个值
    const { funnelMode, value, dimension } = this
    const valueLength = value.length
    const dimensionLength = dimension.length

    return (
      (funnelMode === 'dimension' && valueLength >= 1 && dimensionLength >= 1) ||
      (funnelMode === 'value' && valueLength >= 1)
    )
  }

  // 保存时, 需要保存进接口真实使用的维度
  beforeSaveReport() {
    this.updateDimension([...this.enableDimension])
  }
}
