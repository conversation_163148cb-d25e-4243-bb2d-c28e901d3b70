// uno.config.ts
import { defineConfig, presetUno, transformerDirectives } from 'unocss'
import { defaultTheme, alpha } from '@kui/base'
import omit from 'lodash-es/omit'

const {
  palette: { grey, blue, green, red, geekBlue, purple },
} = defaultTheme

const colors = omit(defaultTheme.palette, [
  'tonalOffset',
  'contrastThreshold',
  'mode',
  'augmentColor',
  'getContrastText',
])

export default defineConfig({
  // ...UnoCSS options
  theme: {
    //@ts-ignore
    colors: {
      ...colors,
      splitLine: alpha(grey[900], 0.1),
      borderLine: alpha(grey[900], 0.15),
      disabledFill: alpha(grey[300], 0.08),
      dimension: green[100],
      measure: geekBlue[100],
      measureValue: purple[100],
      iconNormal: grey[300],
    },
  },
  presets: [presetUno()],
  transformers: [transformerDirectives()],
  shortcuts: [
    // you could still have object style
    {
      'field-wall-wrapper': 'flex flex-col gap-2',
    },
  ],
  /*rules: [
    //@ts-ignore
    [/^bg-grey-(\d+)$/, ([, d]) => ({ 'background-color': grey[d] })],
    //@ts-ignore
    [/^bg-blue-(\d+)$/, ([, d]) => ({ 'background-color': blue[d] })],
  ],*/
})
