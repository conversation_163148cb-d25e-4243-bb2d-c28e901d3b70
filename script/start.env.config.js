/**
 * @file 终端prompts的交互提示，并将value写入process.env
 */

const environments = [
  {
    title: 'vl7y8cbe33 开发环境',
    value: {
      TENANT: 'vl7y8cbe33',
      UAL: 'https://ual-vl7y8cbe33.kylin.shuyun.com',
      ORIGIN: 'http://vl7y8cbe33.kylin.shuyun.com',
    },
  },
  {
    title: 'kylindemo demo环境',
    value: {
      TENANT: 'kylindemo',
      UAL: 'https://ual-kylindemo.kylin.shuyun.com',
      ORIGIN: 'http://kylindemo.kylin.shuyun.com',
    },
  },
  {
    title: 'xttyf5e2f6 测试环境',
    value: {
      TENANT: 'xttyf5e2f6',
      UAL: 'https://ual-xttyf5e2f6.kylin.shuyun.com',
      ORIGIN: 'http://xttyf5e2f6.kylin.shuyun.com',
    },
  },
  {
    title: 'ryt2sj3x4m 集成冒烟环境',
    value: {
      TENANT: 'ryt2sj3x4m',
      UAL: 'https://ryt2sj3x4m.kylin.shuyun.com',
      ORIGIN: 'https://ryt2sj3x4m.kylin.shuyun.com',
    },
  },
  {
    title: 'linefriends online',
    value: {
      TENANT: 'linefriends',
      UAL: 'https://ual-wyw11bwts5.kylin.shuyun.com',
      ORIGIN: 'https://linefriends.kylin.shuyun.com',
    },
  },
  {
    title: 'handingsports online',
    value: {
      TENANT: 'rsnlbntsn5',
      UAL: 'https://ualcrm.handingsports.com',
      ORIGIN: 'https://hdcrm.handingsports.com',
    },
  },
]

const defaultTenant = 'vl7y8cbe33'
const defaultEnv = environments.find((env) => env.value.TENANT === defaultTenant).value

module.exports = {
  defaultEnv,
  environments,
  variants: [
    {
      title: '应用端',
      value: 'business',
      port: 8090,
    },
    {
      title: '实施端',
      value: 'implement',
      port: 8092,
    },{
      title: 'data-agent',
      value: 'data-agent',
      port: 8096,
    },
  ],
}
